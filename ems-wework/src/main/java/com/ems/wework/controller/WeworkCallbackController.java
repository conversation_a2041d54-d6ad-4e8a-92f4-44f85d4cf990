package com.ems.wework.controller;

import com.ems.common.core.controller.BaseController;
import com.ems.common.utils.StringUtils;
import com.ems.wework.service.IWeworkCallbackService;
import com.ems.wework.service.IWeworkCryptoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 企业微信回调接口控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wework/callback")
public class WeworkCallbackController extends BaseController {

    @Autowired
    private IWeworkCallbackService callbackService;
    
    @Autowired
    private IWeworkCryptoService cryptoService;
    
    /**
     * 数据回调 - URL验证（GET请求）
     */
    @GetMapping("/data")
    public String verifyDataCallback(@RequestParam String msg_signature,
                                   @RequestParam String timestamp,
                                   @RequestParam String nonce,
                                   @RequestParam String echostr) {
        try {
//            logger.info("收到数据回调URL验证请求，IP: {}", getClientIP(request));
            return cryptoService.verifyUrl(msg_signature, timestamp, nonce, echostr);
        } catch (Exception e) {
            logger.error("数据回调URL验证失败", e);
            return "error";
        }
    }
    
    /**
     * 数据回调 - 事件推送（POST请求）
     */
    @PostMapping("/data")
    public String handleDataCallback(@RequestBody String encryptData,
                                   @RequestParam String msg_signature,
                                   @RequestParam String timestamp,
                                   @RequestParam String nonce,
                                   HttpServletRequest request) {
        try {
            logger.info("收到数据回调事件推送，IP: {}", getClientIP(request));

            // 1. 验证签名
//            if (!cryptoService.verifySignature(msg_signature, timestamp, nonce, encryptData)) {
//                logger.error("数据回调签名验证失败");
//                return "signature verification failed";
//            }

            // 2. 解密消息（从完整的POST数据中解密）
            String decryptedMsg = cryptoService.decryptMsg(msg_signature, timestamp, nonce, encryptData);
            logger.debug("解密后的数据回调消息: {}", decryptedMsg);
            
            // 3. 根据InfoType处理不同类型的数据回调
            String response = callbackService.handleDataCallback(decryptedMsg);
            
            // 4. 加密响应
            return cryptoService.encrypt(response);
            
        } catch (Exception e) {
            logger.error("处理数据回调失败", e);
            return "error";
        }
    }
    
    /**
     * 指令回调 - URL验证（GET请求）
     */
    @GetMapping("/command")
    public String verifyCommandCallback(@RequestParam String msg_signature,
                                      @RequestParam String timestamp,
                                      @RequestParam String nonce,
                                      @RequestParam String echostr) {
        try {
            logger.info("收到指令回调URL验证请求");
            return cryptoService.verifyUrl(msg_signature, timestamp, nonce, echostr);
        } catch (Exception e) {
            logger.error("指令回调URL验证失败", e);
            return "error";
        }
    }
    
    /**
     * 指令回调 - 事件推送（POST请求）
     */
    @PostMapping("/command")
    public String handleCommandCallback(@RequestBody String encryptData,
                                      @RequestParam String msg_signature,
                                      @RequestParam String timestamp,
                                      @RequestParam String nonce,
                                      HttpServletRequest request) {
        try {
            logger.info("收到指令回调事件推送，msg_signature: {}, timestamp: {}, nonce: {}", msg_signature, timestamp, nonce);
            
            // 1. 验证签名
//            if (!cryptoService.verifySignature(msg_signature, timestamp, nonce, encryptData)) {
//                logger.error("指令回调签名验证失败");
//                return "signature verification failed";
//            }
            
            // 2. 解密消息（从完整的POST数据中解密）
            String decryptedMsg = cryptoService.decryptMsg(msg_signature, timestamp, nonce, encryptData);
            logger.debug("解密后的指令回调消息: {}", decryptedMsg);
            
            // 3. 处理指令回调事件
            String response = callbackService.handleCommandCallback(decryptedMsg);
            logger.debug("处理后的指令回调响应: {}", response);
            
            // 4. 加密响应
//            return cryptoService.encrypt(response);
            return response;
            
        } catch (Exception e) {
            logger.error("处理指令回调失败", e);
            return "error";
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIP(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        String xRealIP = request.getHeader("X-Real-IP");
        String remoteAddr = request.getRemoteAddr();

        if (StringUtils.isNotEmpty(xForwardedFor) && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            // 多次反向代理后会有多个IP值，第一个为真实IP
            int index = xForwardedFor.indexOf(',');
            if (index != -1) {
                return xForwardedFor.substring(0, index);
            } else {
                return xForwardedFor;
            }
        } else if (StringUtils.isNotEmpty(xRealIP) && !"unknown".equalsIgnoreCase(xRealIP)) {
            return xRealIP;
        } else {
            return remoteAddr;
        }
    }
}