package com.ems.wework.service.impl;

import com.ems.common.utils.StringUtils;
import com.ems.wework.config.WeworkConfig;
import com.ems.wework.service.IWeworkCryptoService;
import com.ems.wework.utils.WeworkCryptoUtils;
import com.ems.wework.utils.WXBizMsgCrypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 企业微信加解密服务实现
 * 
 * <AUTHOR>
 */
@Service
public class WeworkCryptoServiceImpl implements IWeworkCryptoService {
    
    private static final Logger log = LoggerFactory.getLogger(WeworkCryptoServiceImpl.class);
    
    @Autowired
    private WeworkConfig weworkConfig;
    
    @Override
    public String verifyUrl(String msgSignature, String timestamp, String nonce, String echostr) {
        try {
            log.debug("开始验证回调URL，msgSignature: {}, timestamp: {}, nonce: {}, echostr: {}", msgSignature, timestamp, nonce, echostr);

            // 使用官方加解密类进行URL验证
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(
                    weworkConfig.getCallback().getToken(),
                    weworkConfig.getCallback().getEncodingAesKey(),
                    weworkConfig.getCorpId() // 使用正确的receiverId
            );

            String decryptedEchostr = wxcpt.VerifyURL(msgSignature, timestamp, nonce, echostr);

            log.debug("回调URL验证成功，解密后的echostr: {}", decryptedEchostr);
            return decryptedEchostr;

        } catch (Exception e) {
            log.error("回调URL验证失败", e);
            throw new RuntimeException("回调URL验证失败", e);
        }
    }
    
    @Override
    public boolean verifySignature(String msgSignature, String timestamp, String nonce, String encryptData) {
        try {
            if (StringUtils.isEmpty(msgSignature) || StringUtils.isEmpty(timestamp) || 
                StringUtils.isEmpty(nonce) || StringUtils.isEmpty(encryptData)) {
                log.warn("签名验证参数不完整");
                return false;
            }
            
            String token = weworkConfig.getCallback().getToken();
             if (StringUtils.isEmpty(token)) {
                log.error("企业微信回调Token未配置");
                return false;
            }
            
            boolean result = WeworkCryptoUtils.verifySignature(msgSignature, token, timestamp, nonce, encryptData);
            log.debug("签名验证结果: {}", result);
            
            return result;
            
        } catch (Exception e) {
            log.error("签名验证异常", e);
            return false;
        }
    }
    
    @Override
    public String decrypt(String encryptData) {
        try {
            if (StringUtils.isEmpty(encryptData)) {
                throw new IllegalArgumentException("加密数据不能为空");
            }

            // 使用官方加解密类进行解密
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(
                    weworkConfig.getCallback().getToken(),
                    weworkConfig.getCallback().getEncodingAesKey(),
                    weworkConfig.getReceiverId() // 使用正确的receiverId
            );

            String decryptedMsg = wxcpt.decrypt(encryptData);
            log.debug("消息解密成功，原始长度: {}, 解密后长度: {}", encryptData.length(), decryptedMsg.length());

            return decryptedMsg;

        } catch (Exception e) {
            log.error("消息解密失败", e);
            throw new RuntimeException("消息解密失败", e);
        }
    }

    @Override
    public String decryptMsg(String msgSignature, String timestamp, String nonce, String postData) {
        try {
            if (StringUtils.isEmpty(postData)) {
                throw new IllegalArgumentException("POST数据不能为空");
            }

            // 使用官方加解密类进行解密（包含签名验证）
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(
                    weworkConfig.getCallback().getToken(),
                    weworkConfig.getCallback().getEncodingAesKey(),
                    weworkConfig.getReceiverId() // 使用正确的receiverId
            );

            String decryptedMsg = wxcpt.DecryptMsg(msgSignature, timestamp, nonce, postData);
            log.debug("回调消息解密成功");

            return decryptedMsg;

        } catch (Exception e) {
            log.error("回调消息解密失败", e);
            throw new RuntimeException("回调消息解密失败", e);
        }
    }

    @Override
    public String encrypt(String responseMsg) {
        try {
            if (StringUtils.isEmpty(responseMsg)) {
                throw new IllegalArgumentException("响应消息不能为空");
            }

            // 使用官方加解密类进行加密
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(
                    weworkConfig.getCallback().getToken(),
                    weworkConfig.getCallback().getEncodingAesKey(),
                    weworkConfig.getReceiverId() // 使用正确的receiverId
            );

            String encryptedMsg = wxcpt.encrypt(wxcpt.getRandomStr(), responseMsg);
            log.debug("消息加密成功，原始长度: {}, 加密后长度: {}", responseMsg.length(), encryptedMsg.length());

            return encryptedMsg;

        } catch (Exception e) {
            log.error("消息加密失败", e);
            throw new RuntimeException("消息加密失败", e);
        }
    }
    
    @Override
    public String generateResponse(String responseMsg, String timestamp, String nonce) {
        try {
            // 使用官方加解密类生成完整的加密响应
            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(
                    weworkConfig.getCallback().getToken(),
                    weworkConfig.getCallback().getEncodingAesKey(),
                    weworkConfig.getReceiverId() // 使用正确的receiverId
            );

            String xmlResponse = wxcpt.EncryptMsg(responseMsg, timestamp, nonce);

            log.debug("生成回调响应成功");
            return xmlResponse;

        } catch (Exception e) {
            log.error("生成回调响应失败", e);
            throw new RuntimeException("生成回调响应失败", e);
        }
    }
}
