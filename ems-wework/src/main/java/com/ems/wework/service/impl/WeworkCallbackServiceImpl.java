package com.ems.wework.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ems.common.utils.StringUtils;
import com.ems.common.utils.ip.IpUtils;
import com.ems.wework.config.WeworkConfig;
import com.ems.wework.constants.WeworkConstants;
import com.ems.wework.domain.WeworkCallbackLog;
import com.ems.wework.mapper.WeworkCallbackLogMapper;
import com.ems.wework.service.IWeworkApiService;
import com.ems.wework.service.IWeworkCallbackService;
import com.ems.wework.service.IWeworkTicketService;
import com.ems.wework.utils.WeworkXmlUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 企业微信回调服务实现
 * 
 * <AUTHOR>
 */
@Service
public class WeworkCallbackServiceImpl implements IWeworkCallbackService {

    private static final Logger log = LoggerFactory.getLogger(WeworkCallbackServiceImpl.class);

    @Autowired
    private IWeworkTicketService ticketService;

    @Autowired
    private WeworkCallbackLogMapper callbackLogMapper;

    @Autowired(required = false)
    private HttpServletRequest request;

    @Autowired
    private IWeworkApiService apiService;

    @Autowired
    private WeworkConfig weworkConfig;

    @Override
    public String handleCommandCallback(String decryptedMsg) {
        long startTime = System.currentTimeMillis();
        String response = "";
        String errorMsg = null;
        String processStatus = "1";

        try {
            log.info("开始处理数据回调：{}", decryptedMsg);

            // 解析XML消息
            Map<String, String> msgMap = WeworkXmlUtils.parseXmlToMap(decryptedMsg);
            String infoType = msgMap.get("InfoType");

            if (StringUtils.isEmpty(infoType)) {
                throw new RuntimeException("InfoType不能为空");
            }

            // 根据InfoType分发处理
            boolean result = false;
            switch (infoType) {
                case WeworkConstants.InfoType.SUITE_TICKET:
                    result = handleSuiteTicket(decryptedMsg);
                    break;
                case WeworkConstants.InfoType.CREATE_AUTH:
                    result = handleCreateAuth(decryptedMsg);
                    break;
                case WeworkConstants.InfoType.CHANGE_AUTH:
                    result = handleChangeAuth(decryptedMsg);
                    break;
                case WeworkConstants.InfoType.CANCEL_AUTH:
                    result = handleCancelAuth(decryptedMsg);
                    break;
                case WeworkConstants.InfoType.CHANGE_CONTACT:
                    result = handleChangeContact(decryptedMsg);
                    break;
                case WeworkConstants.InfoType.SHARE_CHAIN_CHANGE:
                    result = handleShareChainChange(decryptedMsg);
                    break;
                case WeworkConstants.InfoType.TEMPLATE_CARD_EVENT:
                    result = handleTemplateCardEvent(decryptedMsg);
                    break;
                case WeworkConstants.InfoType.TEMPLATE_CARD_MENU_EVENT:
                    result = handleTemplateCardMenuEvent(decryptedMsg);
                    break;
                default:
                    log.warn("未知的InfoType: {}", infoType);
                    result = true; // 未知类型也返回成功，避免重复推送
            }

            if (!result) {
                processStatus = "0";
                errorMsg = "处理失败";
            }

            // 数据回调通常返回"success"表示处理成功
            response = "success";

            // 记录日志
            logCallback(null, null, infoType, msgMap.get("SuiteId"), null,
                    msgMap.get("AgentID"), decryptedMsg, response, processStatus,
                    errorMsg, System.currentTimeMillis() - startTime,
                    getRequestIp(), getUserAgent());

        } catch (Exception e) {
            log.error("处理数据回调失败", e);
            processStatus = "0";
            errorMsg = e.getMessage();
            response = "error";

            // 记录错误日志
            logCallback(null, null, null, null, null, null, decryptedMsg,
                    response, processStatus, errorMsg,
                    System.currentTimeMillis() - startTime,
                    getRequestIp(), getUserAgent());
        }

        return response;
    }

    @Override
    public String handleDataCallback(String decryptedMsg) {
        long startTime = System.currentTimeMillis();
        String response = "";
        String errorMsg = null;
        String processStatus = "1";

        try {
            log.info("开始处理指令回调：{}", decryptedMsg);

            // 解析XML消息
            Map<String, String> msgMap = WeworkXmlUtils.parseXmlToMap(decryptedMsg);
            String msgType = msgMap.get("MsgType");
            String eventType = msgMap.get("Event");

            // 根据消息类型和事件类型处理
            if (WeworkConstants.MsgType.EVENT.equals(msgType)) {
                // 处理事件消息
                response = handleEventMessage(msgMap);
            } else {
                // 处理普通消息
                response = handleNormalMessage(msgMap);
            }

            // 记录日志
            logCallback(msgType, eventType, null, msgMap.get("FromUserName"),
                    msgMap.get("ToUserName"), msgMap.get("AgentID"),
                    decryptedMsg, response, processStatus, errorMsg,
                    System.currentTimeMillis() - startTime,
                    getRequestIp(), getUserAgent());

        } catch (Exception e) {
            log.error("处理指令回调失败", e);
            processStatus = "0";
            errorMsg = e.getMessage();
//            response = WeworkXmlUtils.createEmptyResponse();
            response = errorMsg;

            // 记录错误日志
            logCallback(null, null, null, null, null, null, decryptedMsg,
                    response, processStatus, errorMsg,
                    System.currentTimeMillis() - startTime,
                    getRequestIp(), getUserAgent());
        }

        return response;
    }

    @Override
    public boolean handleSuiteTicket(String suiteTicketInfo) {
        try {
            Map<String, String> msgMap = WeworkXmlUtils.parseXmlToMap(suiteTicketInfo);
            String suiteId = msgMap.get("SuiteId");
            String suiteTicket = msgMap.get("SuiteTicket");

            if (StringUtils.isEmpty(suiteId) || StringUtils.isEmpty(suiteTicket)) {
                log.error("suite_ticket信息不完整：suiteId={}, suiteTicket={}", suiteId, suiteTicket);
                return false;
            }

            // 保存suite_ticket到缓存
            ticketService.saveSuiteTicket(suiteId, suiteTicket);
            // 获取getSuiteAccessToken
            apiService.getSuiteAccessToken(suiteId, weworkConfig.getSuiteSecret(), suiteTicket);
            log.info("处理suite_ticket成功：suiteId={}", suiteId);
            return true;

        } catch (Exception e) {
            log.error("处理suite_ticket失败", e);
            return false;
        }
    }

    @Override
    public boolean handleCreateAuth(String authInfo) {
        try {
            log.info("处理授权成功事件：{}", authInfo);

            Map<String, String> msgMap = WeworkXmlUtils.parseXmlToMap(authInfo);
            String authCode = msgMap.get("AuthCode");
            String suiteId = msgMap.get("SuiteId");

            // 使用AuthCode获取企业永久授权码和授权信息
            String permanentCode = apiService.getPermanentCode(ticketService.getSuiteAccessToken(suiteId), authCode);
            log.info("获取到企业永久授权码：{}", permanentCode);


            log.info("处理授权成功事件完成：suiteId={}, authCode={}", suiteId, authCode);
            return true;

        } catch (Exception e) {
            log.error("处理授权成功事件失败", e);
            return false;
        }
    }

    @Override
    public boolean handleChangeAuth(String authInfo) {
        try {
            log.info("处理授权变更事件：{}", authInfo);
            // TODO: 实现授权变更处理逻辑
            return true;
        } catch (Exception e) {
            log.error("处理授权变更事件失败", e);
            return false;
        }
    }

    @Override
    public boolean handleCancelAuth(String authInfo) {
        try {
            log.info("处理授权取消事件：{}", authInfo);

            Map<String, String> msgMap = WeworkXmlUtils.parseXmlToMap(authInfo);
            String corpId = msgMap.get("AuthCorpId");

            // 清理相关缓存
            if (StringUtils.isNotEmpty(corpId)) {
                ticketService.removeCorpCache(corpId);
            }

            // TODO: 实现授权取消处理逻辑（如禁用相关功能、清理数据等）

            log.info("处理授权取消事件完成：corpId={}", corpId);
            return true;

        } catch (Exception e) {
            log.error("处理授权取消事件失败", e);
            return false;
        }
    }

    @Override
    public boolean handleChangeContact(String contactInfo) {
        try {
            log.info("处理通讯录变更事件：{}", contactInfo);
            // TODO: 实现通讯录变更处理逻辑
            return true;
        } catch (Exception e) {
            log.error("处理通讯录变更事件失败", e);
            return false;
        }
    }

    @Override
    public boolean handleShareChainChange(String shareInfo) {
        try {
            log.info("处理共享应用事件：{}", shareInfo);
            // TODO: 实现共享应用事件处理逻辑
            return true;
        } catch (Exception e) {
            log.error("处理共享应用事件失败", e);
            return false;
        }
    }

    @Override
    public boolean handleTemplateCardEvent(String cardInfo) {
        try {
            log.info("处理模板卡片事件：{}", cardInfo);
            // TODO: 实现模板卡片事件处理逻辑
            return true;
        } catch (Exception e) {
            log.error("处理模板卡片事件失败", e);
            return false;
        }
    }

    @Override
    public boolean handleTemplateCardMenuEvent(String menuInfo) {
        try {
            log.info("处理模板卡片菜单事件：{}", menuInfo);
            // TODO: 实现模板卡片菜单事件处理逻辑
            return true;
        } catch (Exception e) {
            log.error("处理模板卡片菜单事件失败", e);
            return false;
        }
    }

    /**
     * 处理事件消息
     */
    private String handleEventMessage(Map<String, String> msgMap) {
        String eventType = msgMap.get("Event");

        switch (eventType) {
            case WeworkConstants.EventType.SUBSCRIBE:
                return handleSubscribeEvent(msgMap);
            case WeworkConstants.EventType.UNSUBSCRIBE:
                return handleUnsubscribeEvent(msgMap);
            case WeworkConstants.EventType.CLICK:
                return handleClickEvent(msgMap);
            case WeworkConstants.EventType.VIEW:
                return handleViewEvent(msgMap);
            case WeworkConstants.EventType.ENTER_AGENT:
                return handleEnterAgentEvent(msgMap);
            case WeworkConstants.EventType.LOCATION:
                return handleLocationEvent(msgMap);
            default:
                log.warn("未知的事件类型: {}", eventType);
//                return WeworkXmlUtils.createEmptyResponse();
                return "unknown event type";
        }
    }

    /**
     * 处理普通消息
     */
    private String handleNormalMessage(Map<String, String> msgMap) {
        String msgType = msgMap.get("MsgType");

        switch (msgType) {
            case WeworkConstants.MsgType.TEXT:
                return handleTextMessage(msgMap);
            case WeworkConstants.MsgType.IMAGE:
                return handleImageMessage(msgMap);
            case WeworkConstants.MsgType.VOICE:
                return handleVoiceMessage(msgMap);
            case WeworkConstants.MsgType.VIDEO:
                return handleVideoMessage(msgMap);
            case WeworkConstants.MsgType.FILE:
                return handleFileMessage(msgMap);
            case WeworkConstants.MsgType.LOCATION:
                return handleLocationMessage(msgMap);
            default:
                log.warn("未知的消息类型: {}", msgType);
//                return WeworkXmlUtils.createEmptyResponse();we
                return "unknown msg type";
        }
    }

    // 事件处理方法
    private String handleSubscribeEvent(Map<String, String> msgMap) {
        log.info("用户关注事件：{}", msgMap.get("FromUserName"));
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "欢迎关注！");
    }

    private String handleUnsubscribeEvent(Map<String, String> msgMap) {
        log.info("用户取消关注事件：{}", msgMap.get("FromUserName"));
        return WeworkXmlUtils.createEmptyResponse();
    }

    private String handleClickEvent(Map<String, String> msgMap) {
        String eventKey = msgMap.get("EventKey");
        log.info("菜单点击事件：{}, EventKey: {}", msgMap.get("FromUserName"), eventKey);
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "您点击了菜单：" + eventKey);
    }

    private String handleViewEvent(Map<String, String> msgMap) {
        String eventKey = msgMap.get("EventKey");
        log.info("菜单跳转事件：{}, EventKey: {}", msgMap.get("FromUserName"), eventKey);
        return WeworkXmlUtils.createEmptyResponse();
    }

    private String handleEnterAgentEvent(Map<String, String> msgMap) {
        log.info("进入应用事件：{}", msgMap.get("FromUserName"));
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "欢迎使用本应用！");
    }

    private String handleLocationEvent(Map<String, String> msgMap) {
        String latitude = msgMap.get("Latitude");
        String longitude = msgMap.get("Longitude");
        log.info("位置上报事件：{}, 纬度: {}, 经度: {}", msgMap.get("FromUserName"), latitude, longitude);
        return WeworkXmlUtils.createEmptyResponse();
    }

    // 消息处理方法
    private String handleTextMessage(Map<String, String> msgMap) {
        String content = msgMap.get("Content");
        log.info("收到文本消息：{}, 内容: {}", msgMap.get("FromUserName"), content);
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "您发送的消息是：" + content);
    }

    private String handleImageMessage(Map<String, String> msgMap) {
        String mediaId = msgMap.get("MediaId");
        log.info("收到图片消息：{}, MediaId: {}", msgMap.get("FromUserName"), mediaId);
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "收到您的图片");
    }

    private String handleVoiceMessage(Map<String, String> msgMap) {
        String mediaId = msgMap.get("MediaId");
        log.info("收到语音消息：{}, MediaId: {}", msgMap.get("FromUserName"), mediaId);
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "收到您的语音");
    }

    private String handleVideoMessage(Map<String, String> msgMap) {
        String mediaId = msgMap.get("MediaId");
        log.info("收到视频消息：{}, MediaId: {}", msgMap.get("FromUserName"), mediaId);
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "收到您的视频");
    }

    private String handleFileMessage(Map<String, String> msgMap) {
        String mediaId = msgMap.get("MediaId");
        log.info("收到文件消息：{}, MediaId: {}", msgMap.get("FromUserName"), mediaId);
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "收到您的文件");
    }

    private String handleLocationMessage(Map<String, String> msgMap) {
        String latitude = msgMap.get("Location_X");
        String longitude = msgMap.get("Location_Y");
        log.info("收到位置消息：{}, 纬度: {}, 经度: {}", msgMap.get("FromUserName"), latitude, longitude);
        return WeworkXmlUtils.createSimpleResponse(WeworkConstants.MsgType.TEXT, "收到您的位置信息");
    }

    @Override
    public void logCallback(String msgType, String eventType, String infoType, String fromUser,
                            String toUser, String agentId, String msgContent, String responseContent,
                            String processStatus, String errorMsg, Long processTime,
                            String requestIp, String userAgent) {
        try {
            WeworkCallbackLog log = new WeworkCallbackLog();
            log.setMsgType(msgType);
            log.setEventType(eventType);
            log.setInfoType(infoType);
            log.setFromUser(fromUser);
            log.setToUser(toUser);
            log.setAgentId(agentId);
            log.setMsgContent(msgContent);
            log.setResponseContent(responseContent);
            log.setProcessStatus(processStatus);
            log.setErrorMsg(errorMsg);
            log.setProcessTime(processTime);
            log.setRequestIp(requestIp);
            log.setUserAgent(userAgent);

            callbackLogMapper.insertWeworkCallbackLog(log);
        } catch (Exception e) {
            // 记录日志失败不应该影响主流程
            WeworkCallbackServiceImpl.log.error("记录回调日志失败", e);
        }
    }

    /**
     * 获取请求IP
     */
    private String getRequestIp() {
        if (request != null) {
            return IpUtils.getIpAddr(request);
        }
        return null;
    }

    /**
     * 获取用户代理
     */
    private String getUserAgent() {
        if (request != null) {
            return request.getHeader("User-Agent");
        }
        return null;
    }
}