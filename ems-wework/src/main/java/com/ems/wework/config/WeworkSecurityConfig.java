package com.ems.wework.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;

/**
 * 企业微信安全配置
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
public class WeworkSecurityConfig extends WebSecurityConfigurerAdapter {
    
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.authorizeRequests()
            // 允许企业微信回调接口匿名访问
            .antMatchers("/wework/callback/**").permitAll()
            // 其他请求需要认证
            .anyRequest().authenticated()
            .and()
            // 禁用CSRF保护，企业微信回调需要
            .csrf().disable();
    }
}