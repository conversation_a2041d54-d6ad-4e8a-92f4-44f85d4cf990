package com.ems.wework;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * 企业微信模块启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication
@ComponentScan(basePackages = {"com.ems.wework", "com.ems.common"})
@MapperScan("com.ems.wework.mapper")
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}