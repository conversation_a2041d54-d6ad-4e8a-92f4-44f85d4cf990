server:
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

spring:
  application:
    name: ems-wework-connector
#  datasource:
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: ***********************************************************************************************************
#    username: root
#    password: password
    
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size:  10MB
      # 设置总上传的文件大小
      max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: ${REDIS_HOST:localhost}
    # 端口，默认为6379
    port: ${REDIS_PORT:6379}
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# MyBatis配置
#mybatis:
#  mapper-locations: classpath:mapper/*.xml
#  type-aliases-package: com.ems.wework.domain,com.ems.common.core.domain
#  configuration:
#    map-underscore-to-camel-case: true
# MyBatis配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ems.**
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  global-config:
    db-config:
      logic-delete-field: delFlag # 全局逻辑删除的实体字段名
      logic-delete-value: 0      # 逻辑已删除值(默认为0)
      logic-not-delete-value: 1  # 逻辑未删除值(默认为 1)
# 日志配置
logging:
  level:
    com.ems.wework: debug
    org.springframework: warn

# 企业微信配置
wework:
  # 应用类型：self_built(自建应用) 或 third_party(第三方应用)
  app-type: ${WEWORK_APP_TYPE:third_party}
  # 企业ID（自建应用必填，第三方应用可选）
  corp-id: ${WEWORK_CORP_ID:ww8c6ff55562f1b335}
  # 应用密钥（自建应用必填）
  corp-secret: ${WEWORK_CORP_SECRET:your_corp_secret}
  # 应用ID（自建应用必填）
  agent-id: ${WEWORK_AGENT_ID:your_agent_id}
  # 第三方应用ID（第三方应用必填）
  suite-id: ${WEWORK_SUITE_ID:ww30dfbab30fe9302d}
  # 第三方应用密钥（第三方应用必填）
  suite-secret: ${WEWORK_SUITE_SECRET:I_CrZoSUunLdIrEsJ4-6AXBwuhA5WkjHS6fhalYlzWQ}
  # 回调配置
  callback:
    # 回调Token
    token: ${WEWORK_CALLBACK_TOKEN:qVtXBkwhiHT2VZZnMjS0fGS0eQsD5T}
    # 回调加密密钥
    encoding-aes-key: ${WEWORK_CALLBACK_AES_KEY:AstdsxvPuleKjJBnXDBIYiaZXSxFJFxujKtHd81o3A7}
    # 数据回调URL
    data-url: /wework/callback/data
    # 指令回调URL
    command-url: /wework/callback/command
  # OAuth2配置
  oauth2:
    # 授权回调地址
    redirect-uri: ${WEWORK_REDIRECT_URI:http://localhost:8080/wework/auth/callback}
    # 授权scope
    scope: snsapi_base
    # 登录开关
    enabled: ${WEWORK_LOGIN_ENABLED:true}
  # API配置
  api:
    # API基础URL
    base-url: https://qyapi.weixin.qq.com
    # 连接超时时间（毫秒）
    connect-timeout: 5000
    # 读取超时时间（毫秒）
    read-timeout: 10000
    # 重试次数
    retry-count: 3