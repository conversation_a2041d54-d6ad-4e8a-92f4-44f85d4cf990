<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.wework.mapper.WeworkCallbackLogMapper">
    
    <resultMap type="com.ems.wework.domain.WeworkCallbackLog" id="WeworkCallbackLogResult">
        <result property="logId"            column="log_id"            />
        <result property="msgType"          column="msg_type"          />
        <result property="eventType"        column="event_type"        />
        <result property="infoType"         column="info_type"         />
        <result property="fromUser"         column="from_user"         />
        <result property="toUser"           column="to_user"           />
        <result property="agentId"          column="agent_id"          />
        <result property="msgContent"       column="msg_content"       />
        <result property="responseContent"  column="response_content"  />
        <result property="processStatus"    column="process_status"    />
        <result property="errorMsg"         column="error_msg"         />
        <result property="processTime"      column="process_time"      />
        <result property="requestIp"        column="request_ip"        />
        <result property="userAgent"        column="user_agent"        />
        <result property="createTime"       column="create_time"       />
    </resultMap>

    <sql id="selectWeworkCallbackLogVo">
        select log_id, msg_type, event_type, info_type, from_user, to_user, agent_id, 
               msg_content, response_content, process_status, error_msg, process_time, 
               request_ip, user_agent, create_time 
        from wework_callback_log
    </sql>

    <select id="selectWeworkCallbackLogList" parameterType="com.ems.wework.domain.WeworkCallbackLog" resultMap="WeworkCallbackLogResult">
        <include refid="selectWeworkCallbackLogVo"/>
        <where>  
            <if test="msgType != null  and msgType != ''"> and msg_type = #{msgType}</if>
            <if test="eventType != null  and eventType != ''"> and event_type = #{eventType}</if>
            <if test="infoType != null  and infoType != ''"> and info_type = #{infoType}</if>
            <if test="fromUser != null  and fromUser != ''"> and from_user = #{fromUser}</if>
            <if test="toUser != null  and toUser != ''"> and to_user = #{toUser}</if>
            <if test="agentId != null  and agentId != ''"> and agent_id = #{agentId}</if>
            <if test="processStatus != null  and processStatus != ''"> and process_status = #{processStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectWeworkCallbackLogByLogId" parameterType="Long" resultMap="WeworkCallbackLogResult">
        <include refid="selectWeworkCallbackLogVo"/>
        where log_id = #{logId}
    </select>
        
    <insert id="insertWeworkCallbackLog" parameterType="com.ems.wework.domain.WeworkCallbackLog" useGeneratedKeys="true" keyProperty="logId">
        insert into wework_callback_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="msgType != null">msg_type,</if>
            <if test="eventType != null">event_type,</if>
            <if test="infoType != null">info_type,</if>
            <if test="fromUser != null">from_user,</if>
            <if test="toUser != null">to_user,</if>
            <if test="agentId != null">agent_id,</if>
            <if test="msgContent != null">msg_content,</if>
            <if test="responseContent != null">response_content,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="processTime != null">process_time,</if>
            <if test="requestIp != null">request_ip,</if>
            <if test="userAgent != null">user_agent,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="msgType != null">#{msgType},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="infoType != null">#{infoType},</if>
            <if test="fromUser != null">#{fromUser},</if>
            <if test="toUser != null">#{toUser},</if>
            <if test="agentId != null">#{agentId},</if>
            <if test="msgContent != null">#{msgContent},</if>
            <if test="responseContent != null">#{responseContent},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="requestIp != null">#{requestIp},</if>
            <if test="userAgent != null">#{userAgent},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateWeworkCallbackLog" parameterType="com.ems.wework.domain.WeworkCallbackLog">
        update wework_callback_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="msgType != null">msg_type = #{msgType},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="infoType != null">info_type = #{infoType},</if>
            <if test="fromUser != null">from_user = #{fromUser},</if>
            <if test="toUser != null">to_user = #{toUser},</if>
            <if test="agentId != null">agent_id = #{agentId},</if>
            <if test="msgContent != null">msg_content = #{msgContent},</if>
            <if test="responseContent != null">response_content = #{responseContent},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="requestIp != null">request_ip = #{requestIp},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteWeworkCallbackLogByLogId" parameterType="Long">
        delete from wework_callback_log where log_id = #{logId}
    </delete>

    <delete id="deleteWeworkCallbackLogByLogIds" parameterType="Long">
        delete from wework_callback_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>

    <delete id="cleanOldLogs">
        delete from wework_callback_log where create_time &lt; date_sub(now(), interval #{days} day)
    </delete>
</mapper>