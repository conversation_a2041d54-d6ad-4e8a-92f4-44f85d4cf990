version: '3.8'

services:
  ems-wework-connector:
    build: .
    container_name: ems-wework-connector
    ports:
      - "8080:8080"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - WEWORK_APP_TYPE=third_party
      - WEWORK_CORP_ID=your_corp_id
      - WEWORK_CORP_SECRET=your_corp_secret
      - WEWORK_AGENT_ID=your_agent_id
      - WEWORK_SUITE_ID=your_suite_id
      - WEWORK_SUITE_SECRET=your_suite_secret
      - WEWORK_CALLBACK_TOKEN=your_callback_token
      - WEWORK_CALLBACK_AES_KEY=your_aes_key
      - WEWORK_REDIRECT_URI=http://localhost:8080/wework/auth/callback
      - TZ=Asia/Shanghai
    depends_on:
      - redis
    networks:
      - ems-network
    restart: always

  redis:
    image: redis:6-alpine
    container_name: ems-redis
    ports:
      - "6379:6379"
    networks:
      - ems-network
    restart: always
    command: redis-server --appendonly yes

networks:
  ems-network:
    driver: bridge